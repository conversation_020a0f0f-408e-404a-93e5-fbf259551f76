import 'dart:io';
import 'package:permission_handler/permission_handler.dart';

/// 權限管理服務
/// 
/// 負責處理應用程式所需的各種權限請求，包含：
/// - 相機權限
/// - 相簿存取權限
/// - 儲存權限
class PermissionService {
  /// 請求相機權限
  /// 
  /// 返回 true 表示權限已獲得，false 表示權限被拒絕
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// 請求相簿權限
  /// 
  /// 根據平台和 Android 版本請求適當的權限
  static Future<bool> requestPhotosPermission() async {
    if (Platform.isAndroid) {
      // Android 13+ 使用 READ_MEDIA_IMAGES
      if (await _isAndroid13OrHigher()) {
        final status = await Permission.photos.request();
        return status.isGranted;
      } else {
        // Android 12 以下使用 READ_EXTERNAL_STORAGE
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    } else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }
    return false;
  }

  /// 請求儲存權限
  /// 
  /// 用於將圖片儲存到相簿
  static Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      if (await _isAndroid13OrHigher()) {
        // Android 13+ 不需要額外的儲存權限
        return true;
      } else {
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    } else if (Platform.isIOS) {
      final status = await Permission.photosAddOnly.request();
      return status.isGranted;
    }
    return false;
  }

  /// 檢查是否為 Android 13 或更高版本
  static Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;
    
    // 簡化的版本檢查，實際應用中可能需要更精確的檢查
    try {
      final androidInfo = await _getAndroidVersion();
      return androidInfo >= 33; // Android 13 = API 33
    } catch (e) {
      return false;
    }
  }

  /// 獲取 Android 版本
  static Future<int> _getAndroidVersion() async {
    // 這裡簡化處理，實際應用中可使用 device_info_plus 套件
    return 33; // 預設為 Android 13
  }

  /// 檢查所有必要權限是否已獲得
  static Future<bool> checkAllPermissions() async {
    final cameraGranted = await Permission.camera.isGranted;
    final photosGranted = await Permission.photos.isGranted;
    final storageGranted = await requestStoragePermission();
    
    return cameraGranted && photosGranted && storageGranted;
  }

  /// 開啟應用程式設定頁面
  static Future<void> openAppSettings() async {
    await openAppSettings();
  }
}
