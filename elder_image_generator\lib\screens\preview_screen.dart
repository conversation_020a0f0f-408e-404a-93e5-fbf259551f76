import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../models/text_style_model.dart';
import '../services/image_saver.dart';
import '../widgets/final_image_widget.dart';

/// 預覽畫面
/// 
/// 顯示最終的長輩圖效果，提供儲存和分享功能
class PreviewScreen extends StatefulWidget {
  final Uint8List imageBytes;
  final TextStyleModel textStyle;

  const PreviewScreen({
    super.key,
    required this.imageBytes,
    required this.textStyle,
  });

  @override
  State<PreviewScreen> createState() => _PreviewScreenState();
}

class _PreviewScreenState extends State<PreviewScreen> {
  bool _isProcessing = false;
  Uint8List? _finalImageBytes;

  @override
  void initState() {
    super.initState();
    _generateFinalImage();
  }

  /// 生成最終圖片
  Future<void> _generateFinalImage() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // 這裡應該使用 CustomPaint 或 Canvas 來合成圖片
      // 暫時使用原圖片作為示例
      _finalImageBytes = widget.imageBytes;
    } catch (e) {
      print('生成最終圖片時發生錯誤: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 儲存圖片到相簿
  Future<void> _saveToGallery() async {
    if (_finalImageBytes == null) {
      _showMessage('圖片尚未準備完成');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final success = await ImageSaver.saveToGallery(_finalImageBytes!);
      if (success) {
        _showMessage(AppConstants.successImageSaved);
      } else {
        _showMessage(AppConstants.errorImageSave);
      }
    } catch (e) {
      _showMessage('儲存圖片時發生錯誤');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 分享圖片
  Future<void> _shareImage() async {
    if (_finalImageBytes == null) {
      _showMessage('圖片尚未準備完成');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final success = await ImageSaver.shareImage(_finalImageBytes!);
      if (success) {
        _showMessage(AppConstants.successImageShared);
      } else {
        _showMessage(AppConstants.errorImageShare);
      }
    } catch (e) {
      _showMessage('分享圖片時發生錯誤');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 分享到 LINE
  Future<void> _shareToLine() async {
    if (_finalImageBytes == null) {
      _showMessage('圖片尚未準備完成');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final success = await ImageSaver.shareToLine(_finalImageBytes!);
      if (success) {
        _showMessage('已開啟分享選單');
      } else {
        _showMessage('分享失敗');
      }
    } catch (e) {
      _showMessage('分享到 LINE 時發生錯誤');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 顯示訊息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 返回首頁
  void _goHome() {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.titlePreview),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _goHome,
            icon: const Icon(Icons.home),
            tooltip: '回到首頁',
          ),
        ],
      ),
      body: Column(
        children: [
          // 圖片預覽區域
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.padding),
              child: _isProcessing
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('正在處理圖片...'),
                        ],
                      ),
                    )
                  : _buildImagePreview(),
            ),
          ),
          
          // 操作按鈕區域
          Container(
            padding: const EdgeInsets.all(AppConstants.padding),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(
                top: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: _buildActionButtons(),
          ),
        ],
      ),
    );
  }

  /// 建立圖片預覽
  Widget _buildImagePreview() {
    return Card(
      elevation: 4,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: _finalImageBytes != null
            ? FinalImageWidget(
                imageBytes: _finalImageBytes!,
                textStyle: widget.textStyle,
              )
            : Container(
                height: 300,
                color: Colors.grey[200],
                child: const Center(
                  child: Text('圖片載入中...'),
                ),
              ),
      ),
    );
  }

  /// 建立操作按鈕
  Widget _buildActionButtons() {
    return Column(
      children: [
        // 主要操作按鈕
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isProcessing ? null : _saveToGallery,
                icon: const Icon(Icons.save),
                label: const Text(AppConstants.buttonSave),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isProcessing ? null : _shareImage,
                icon: const Icon(Icons.share),
                label: const Text(AppConstants.buttonShare),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // LINE 分享按鈕
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isProcessing ? null : _shareToLine,
            icon: const Icon(Icons.chat),
            label: const Text('分享到 LINE'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // 重新製作按鈕
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _goHome,
            icon: const Icon(Icons.refresh),
            label: const Text('重新製作'),
          ),
        ),
      ],
    );
  }
}
