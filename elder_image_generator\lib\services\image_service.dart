import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

/// 圖片處理服務
/// 
/// 負責處理圖片相關的操作，包含：
/// - 從相機拍攝圖片
/// - 從相簿選擇圖片
/// - 載入內建圖片
/// - AI 圖片生成
class ImageService {
  static final ImagePicker _picker = ImagePicker();

  /// 從相機拍攝圖片
  /// 
  /// 返回拍攝的圖片檔案，如果取消則返回 null
  static Future<File?> pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );
      
      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      print('從相機選取圖片時發生錯誤: $e');
      return null;
    }
  }

  /// 從相簿選擇圖片
  /// 
  /// 返回選擇的圖片檔案，如果取消則返回 null
  static Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );
      
      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      print('從相簿選取圖片時發生錯誤: $e');
      return null;
    }
  }

  /// 載入內建圖片
  /// 
  /// 從 assets 資料夾載入預設的圖片
  static Future<List<String>> getBuiltInImages() async {
    try {
      // 這裡列出所有內建圖片的路徑
      return [
        'assets/images/sample1.jpg',
        'assets/images/sample2.jpg',
        'assets/images/sample3.jpg',
        'assets/images/sample4.jpg',
        'assets/images/sample5.jpg',
      ];
    } catch (e) {
      print('載入內建圖片時發生錯誤: $e');
      return [];
    }
  }

  /// 從 assets 載入圖片為 Uint8List
  /// 
  /// [assetPath] assets 中的圖片路徑
  static Future<Uint8List?> loadAssetImage(String assetPath) async {
    try {
      final ByteData data = await rootBundle.load(assetPath);
      return data.buffer.asUint8List();
    } catch (e) {
      print('載入 asset 圖片時發生錯誤: $e');
      return null;
    }
  }

  /// 使用 AI 生成圖片
  /// 
  /// 使用 pollinations.ai 免費 API 生成圖片
  /// [prompt] 圖片描述提示詞
  /// [width] 圖片寬度，預設 512
  /// [height] 圖片高度，預設 512
  static Future<Uint8List?> generateAIImage(
    String prompt, {
    int width = 512,
    int height = 512,
  }) async {
    try {
      // 使用 pollinations.ai 免費 API
      final encodedPrompt = Uri.encodeComponent(prompt);
      final url = 'https://image.pollinations.ai/prompt/$encodedPrompt'
          '?width=$width&height=$height&nologo=true';
      
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        return response.bodyBytes;
      } else {
        print('AI 圖片生成失敗: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('AI 圖片生成時發生錯誤: $e');
      return null;
    }
  }

  /// 將檔案轉換為 Uint8List
  /// 
  /// [file] 要轉換的圖片檔案
  static Future<Uint8List?> fileToUint8List(File file) async {
    try {
      return await file.readAsBytes();
    } catch (e) {
      print('檔案轉換時發生錯誤: $e');
      return null;
    }
  }

  /// 驗證圖片檔案是否有效
  /// 
  /// [file] 要驗證的圖片檔案
  static Future<bool> isValidImageFile(File file) async {
    try {
      final bytes = await file.readAsBytes();
      return bytes.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
