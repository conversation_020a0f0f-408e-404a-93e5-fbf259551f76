import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../services/permission_service.dart';
import 'image_selection_screen.dart';

/// 主畫面
/// 
/// 應用程式的首頁，提供簡潔的操作介面
/// 採用大按鈕設計，方便長輩使用
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  /// 檢查並請求必要權限
  Future<void> _checkPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 檢查權限狀態
      final hasPermissions = await PermissionService.checkAllPermissions();
      
      if (!hasPermissions) {
        // 顯示權限說明對話框
        if (mounted) {
          _showPermissionDialog();
        }
      }
    } catch (e) {
      print('檢查權限時發生錯誤: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 顯示權限說明對話框
  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('權限說明'),
        content: const Text(
          '為了正常使用長輩圖產生器，需要以下權限：\n\n'
          '• 相機權限：用於拍攝照片\n'
          '• 相簿權限：用於選擇和儲存照片\n\n'
          '請點擊「同意」來授予權限。',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _requestPermissions();
            },
            child: const Text('同意'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('稍後'),
          ),
        ],
      ),
    );
  }

  /// 請求權限
  Future<void> _requestPermissions() async {
    await PermissionService.requestCameraPermission();
    await PermissionService.requestPhotosPermission();
    await PermissionService.requestStoragePermission();
  }

  /// 開始製作長輩圖
  void _startCreating() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ImageSelectionScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : _buildBody(),
    );
  }

  /// 建立主要內容
  Widget _buildBody() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.padding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 應用程式圖示和標題
          _buildHeader(),
          
          const SizedBox(height: 48),
          
          // 主要按鈕
          _buildMainButton(),
          
          const SizedBox(height: 24),
          
          // 說明文字
          _buildDescription(),
          
          const Spacer(),
          
          // 版本資訊
          _buildVersionInfo(),
        ],
      ),
    );
  }

  /// 建立標題區域
  Widget _buildHeader() {
    return Column(
      children: [
        // 應用程式圖示
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.red.shade100,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: Colors.red,
              width: 3,
            ),
          ),
          child: const Icon(
            Icons.image,
            size: 64,
            color: Colors.red,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // 應用程式標題
        Text(
          AppConstants.appName,
          style: Theme.of(context).textTheme.headlineLarge,
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 8),
        
        // 副標題
        Text(
          '簡單製作專屬長輩圖',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 建立主要按鈕
  Widget _buildMainButton() {
    return SizedBox(
      width: double.infinity,
      height: 80,
      child: ElevatedButton(
        onPressed: _startCreating,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          textStyle: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate, size: 32),
            SizedBox(width: 12),
            Text('開始製作'),
          ],
        ),
      ),
    );
  }

  /// 建立說明文字
  Widget _buildDescription() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              '使用步驟',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            _buildStep('1', '選擇圖片', '從相機、相簿或內建圖片中選擇'),
            _buildStep('2', '編輯文字', '輸入祝福語並調整樣式'),
            _buildStep('3', '儲存分享', '儲存到相簿或分享給親友'),
          ],
        ),
      ),
    );
  }

  /// 建立步驟說明
  Widget _buildStep(String number, String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 建立版本資訊
  Widget _buildVersionInfo() {
    return Text(
      '版本 ${AppConstants.appVersion}',
      style: TextStyle(
        color: Colors.grey[500],
        fontSize: 12,
      ),
    );
  }
}
