import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'screens/home_screen.dart';
import 'utils/constants.dart';

void main() {
  runApp(const ElderImageGeneratorApp());
}

/// 長輩圖產生器主應用程式
class ElderImageGeneratorApp extends StatelessWidget {
  const ElderImageGeneratorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: _buildTheme(),
      home: const HomeScreen(),
    );
  }

  /// 建立應用程式主題
  ///
  /// 採用大字體、高對比度的長輩友善設計
  ThemeData _buildTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.red,
        brightness: Brightness.light,
      ),

      // 字體設定
      textTheme: GoogleFonts.notoSansTcTextTheme().copyWith(
        headlineLarge: GoogleFonts.notoSansTc(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
        headlineMedium: GoogleFonts.notoSansTc(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
        titleLarge: GoogleFonts.notoSansTc(
          fontSize: AppConstants.titleFontSize,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
        bodyLarge: GoogleFonts.notoSansTc(
          fontSize: AppConstants.buttonFontSize,
          color: Colors.black87,
        ),
        bodyMedium: GoogleFonts.notoSansTc(
          fontSize: AppConstants.subtitleFontSize,
          color: Colors.black87,
        ),
      ),

      // 按鈕主題
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
          textStyle: GoogleFonts.notoSansTc(
            fontSize: AppConstants.buttonFontSize,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
        ),
      ),

      // 卡片主題
      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),

      // AppBar 主題
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        titleTextStyle: GoogleFonts.notoSansTc(
          fontSize: AppConstants.titleFontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        elevation: 2,
      ),
    );
  }
}
