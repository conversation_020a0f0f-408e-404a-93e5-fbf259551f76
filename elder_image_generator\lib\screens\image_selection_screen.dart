import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../services/image_service.dart';
import '../services/permission_service.dart';
import 'text_editing_screen.dart';

/// 圖片選擇畫面
/// 
/// 提供多種圖片來源選擇：
/// - 相機拍攝
/// - 相簿選擇
/// - 內建圖片
/// - AI 生成圖片
class ImageSelectionScreen extends StatefulWidget {
  const ImageSelectionScreen({super.key});

  @override
  State<ImageSelectionScreen> createState() => _ImageSelectionScreenState();
}

class _ImageSelectionScreenState extends State<ImageSelectionScreen> {
  bool _isLoading = false;
  String _loadingMessage = '';

  /// 從相機拍攝圖片
  Future<void> _pickFromCamera() async {
    // 檢查相機權限
    final hasPermission = await PermissionService.requestCameraPermission();
    if (!hasPermission) {
      _showErrorDialog(AppConstants.errorCameraPermission);
      return;
    }

    setState(() {
      _isLoading = true;
      _loadingMessage = '正在開啟相機...';
    });

    try {
      final imageFile = await ImageService.pickImageFromCamera();
      if (imageFile != null) {
        final imageBytes = await ImageService.fileToUint8List(imageFile);
        if (imageBytes != null) {
          _navigateToTextEditing(imageBytes);
        }
      }
    } catch (e) {
      _showErrorDialog('拍攝照片時發生錯誤');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 從相簿選擇圖片
  Future<void> _pickFromGallery() async {
    // 檢查相簿權限
    final hasPermission = await PermissionService.requestPhotosPermission();
    if (!hasPermission) {
      _showErrorDialog(AppConstants.errorPhotosPermission);
      return;
    }

    setState(() {
      _isLoading = true;
      _loadingMessage = '正在開啟相簿...';
    });

    try {
      final imageFile = await ImageService.pickImageFromGallery();
      if (imageFile != null) {
        final imageBytes = await ImageService.fileToUint8List(imageFile);
        if (imageBytes != null) {
          _navigateToTextEditing(imageBytes);
        }
      }
    } catch (e) {
      _showErrorDialog('選擇照片時發生錯誤');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 顯示內建圖片選擇
  void _showBuiltInImages() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildBuiltInImagesSheet(),
    );
  }

  /// 顯示 AI 圖片生成對話框
  void _showAIImageDialog() {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('AI 圖片生成'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('請輸入圖片描述（建議使用英文）：'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                hintText: '例如：beautiful sunset landscape',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _generateAIImage(controller.text);
            },
            child: const Text('生成'),
          ),
        ],
      ),
    );
  }

  /// 生成 AI 圖片
  Future<void> _generateAIImage(String prompt) async {
    if (prompt.trim().isEmpty) {
      _showErrorDialog('請輸入圖片描述');
      return;
    }

    setState(() {
      _isLoading = true;
      _loadingMessage = '正在生成 AI 圖片...';
    });

    try {
      final imageBytes = await ImageService.generateAIImage(prompt);
      if (imageBytes != null) {
        _navigateToTextEditing(imageBytes);
      } else {
        _showErrorDialog(AppConstants.errorAIGenerate);
      }
    } catch (e) {
      _showErrorDialog('生成 AI 圖片時發生錯誤');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 選擇內建圖片
  Future<void> _selectBuiltInImage(String assetPath) async {
    Navigator.of(context).pop(); // 關閉底部選單

    setState(() {
      _isLoading = true;
      _loadingMessage = '正在載入圖片...';
    });

    try {
      final imageBytes = await ImageService.loadAssetImage(assetPath);
      if (imageBytes != null) {
        _navigateToTextEditing(imageBytes);
      } else {
        _showErrorDialog(AppConstants.errorImageLoad);
      }
    } catch (e) {
      _showErrorDialog('載入內建圖片時發生錯誤');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 導航到文字編輯畫面
  void _navigateToTextEditing(Uint8List imageBytes) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TextEditingScreen(imageBytes: imageBytes),
      ),
    );
  }

  /// 顯示錯誤對話框
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('錯誤'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.titleSelectImage),
        centerTitle: true,
      ),
      body: _isLoading
          ? _buildLoadingWidget()
          : _buildSelectionOptions(),
    );
  }

  /// 建立載入中的 Widget
  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            _loadingMessage,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }

  /// 建立選擇選項
  Widget _buildSelectionOptions() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.padding),
      child: Column(
        children: [
          // 標題說明
          Text(
            '請選擇圖片來源',
            style: Theme.of(context).textTheme.headlineMedium,
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 32),
          
          // 選項按鈕
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildOptionCard(
                  icon: Icons.camera_alt,
                  title: AppConstants.buttonCamera,
                  subtitle: '拍攝新照片',
                  onTap: _pickFromCamera,
                ),
                _buildOptionCard(
                  icon: Icons.photo_library,
                  title: AppConstants.buttonGallery,
                  subtitle: '從相簿選擇',
                  onTap: _pickFromGallery,
                ),
                _buildOptionCard(
                  icon: Icons.collections,
                  title: AppConstants.buttonBuiltIn,
                  subtitle: '使用內建圖片',
                  onTap: _showBuiltInImages,
                ),
                _buildOptionCard(
                  icon: Icons.auto_awesome,
                  title: AppConstants.buttonAI,
                  subtitle: 'AI 智能生成',
                  onTap: _showAIImageDialog,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 建立選項卡片
  Widget _buildOptionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: Colors.red,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 建立內建圖片選擇底部選單
  Widget _buildBuiltInImagesSheet() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 標題
          Text(
            '選擇內建圖片',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          
          // 圖片網格
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: AppConstants.builtInImages.length,
              itemBuilder: (context, index) {
                final imagePath = AppConstants.builtInImages[index];
                return _buildBuiltInImageCard(imagePath);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 建立內建圖片卡片
  Widget _buildBuiltInImageCard(String imagePath) {
    return Card(
      child: InkWell(
        onTap: () => _selectBuiltInImage(imagePath),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            color: Colors.grey[200],
          ),
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 8),
              Text(
                '示例圖片',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
