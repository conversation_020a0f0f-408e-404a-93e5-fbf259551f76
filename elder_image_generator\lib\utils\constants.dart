import 'package:flutter/material.dart';

/// 應用程式常數
class AppConstants {
  // 應用程式資訊
  static const String appName = '長輩圖產生器';
  static const String appVersion = '1.0.0';
  
  // 預設祝福語
  static const List<String> defaultBlessings = [
    '身體健康',
    '年年有餘',
    '福如東海',
    '壽比南山',
    '恭喜發財',
    '心想事成',
    '萬事如意',
    '平安喜樂',
    '�闔家歡樂',
    '步步高升',
    '財源廣進',
    '吉祥如意',
    '龍馬精神',
    '笑口常開',
    '健康長壽',
  ];
  
  // 顏色配置
  static const List<Color> textColors = [
    Colors.red,
    Colors.yellow,
    Colors.white,
    Colors.black,
    Colors.blue,
    Colors.green,
    Colors.purple,
    Colors.orange,
    Colors.pink,
    Colors.brown,
  ];
  
  static const List<Color> backgroundColors = [
    Colors.black54,
    Colors.white70,
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.yellow,
    Colors.purple,
    Colors.orange,
    Colors.transparent,
  ];
  
  // 字體大小選項
  static const List<double> fontSizes = [
    16.0,
    20.0,
    24.0,
    28.0,
    32.0,
    36.0,
    40.0,
    48.0,
    56.0,
    64.0,
  ];
  
  // 字體粗細選項
  static const List<FontWeight> fontWeights = [
    FontWeight.normal,
    FontWeight.bold,
    FontWeight.w900,
  ];
  
  // 文字對齊選項
  static const List<TextAlign> textAligns = [
    TextAlign.left,
    TextAlign.center,
    TextAlign.right,
  ];
  
  // UI 尺寸
  static const double buttonHeight = 60.0;
  static const double buttonFontSize = 18.0;
  static const double titleFontSize = 24.0;
  static const double subtitleFontSize = 16.0;
  static const double padding = 16.0;
  static const double borderRadius = 12.0;
  
  // 圖片設定
  static const double maxImageWidth = 1920.0;
  static const double maxImageHeight = 1920.0;
  static const int imageQuality = 85;
  
  // AI 圖片生成設定
  static const int aiImageWidth = 512;
  static const int aiImageHeight = 512;
  static const String aiImageBaseUrl = 'https://image.pollinations.ai/prompt/';
  
  // 內建圖片路徑
  static const List<String> builtInImages = [
    'assets/images/sample1.jpg',
    'assets/images/sample2.jpg',
    'assets/images/sample3.jpg',
    'assets/images/sample4.jpg',
    'assets/images/sample5.jpg',
  ];
  
  // 錯誤訊息
  static const String errorCameraPermission = '需要相機權限才能拍攝照片';
  static const String errorPhotosPermission = '需要相簿權限才能選擇照片';
  static const String errorStoragePermission = '需要儲存權限才能儲存圖片';
  static const String errorImageLoad = '載入圖片失敗';
  static const String errorImageSave = '儲存圖片失敗';
  static const String errorImageShare = '分享圖片失敗';
  static const String errorAIGenerate = 'AI 圖片生成失敗';
  static const String errorNetworkConnection = '網路連線失敗';
  
  // 成功訊息
  static const String successImageSaved = '圖片已成功儲存到相簿';
  static const String successImageShared = '圖片分享成功';
  
  // 提示訊息
  static const String hintSelectImage = '請選擇一張圖片';
  static const String hintEnterText = '請輸入祝福語';
  static const String hintDragText = '拖曳文字到想要的位置';
  static const String hintAdjustStyle = '調整文字樣式';
  
  // 按鈕文字
  static const String buttonCamera = '拍照';
  static const String buttonGallery = '相簿';
  static const String buttonBuiltIn = '內建圖片';
  static const String buttonAI = 'AI 生成';
  static const String buttonSave = '儲存';
  static const String buttonShare = '分享';
  static const String buttonNext = '下一步';
  static const String buttonBack = '上一步';
  static const String buttonDone = '完成';
  static const String buttonCancel = '取消';
  static const String buttonConfirm = '確認';
  static const String buttonRetry = '重試';
  
  // 標題文字
  static const String titleSelectImage = '選擇圖片';
  static const String titleEditText = '編輯文字';
  static const String titlePreview = '預覽';
  static const String titleSettings = '設定';
}
