import 'dart:io';
import 'dart:typed_data';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

/// 圖片儲存與分享服務
/// 
/// 負責處理圖片的儲存和分享功能，包含：
/// - 儲存圖片到相簿
/// - 分享圖片到其他應用程式
/// - 處理平台權限問題
class ImageSaver {
  /// 儲存圖片到相簿
  /// 
  /// [imageBytes] 圖片的位元組資料
  /// [fileName] 檔案名稱，預設為時間戳記
  /// 
  /// 返回 true 表示儲存成功，false 表示儲存失敗
  static Future<bool> saveToGallery(
    Uint8List imageBytes, {
    String? fileName,
  }) async {
    try {
      // 檢查並請求權限
      if (!await _checkStoragePermission()) {
        print('儲存權限未獲得');
        return false;
      }

      // 生成檔案名稱
      final name = fileName ?? 
          '長輩圖_${DateTime.now().millisecondsSinceEpoch}.jpg';

      // 儲存到相簿
      final result = await ImageGallerySaver.saveImage(
        imageBytes,
        name: name,
        quality: 90,
      );

      // 檢查儲存結果
      if (result['isSuccess'] == true) {
        print('圖片已成功儲存到相簿');
        return true;
      } else {
        print('圖片儲存失敗: ${result['errorMessage']}');
        return false;
      }
    } catch (e) {
      print('儲存圖片時發生錯誤: $e');
      return false;
    }
  }

  /// 分享圖片
  /// 
  /// [imageBytes] 圖片的位元組資料
  /// [fileName] 檔案名稱，預設為時間戳記
  /// [text] 分享時的文字內容
  /// 
  /// 返回 true 表示分享成功，false 表示分享失敗
  static Future<bool> shareImage(
    Uint8List imageBytes, {
    String? fileName,
    String? text,
  }) async {
    try {
      // 生成檔案名稱
      final name = fileName ?? 
          '長輩圖_${DateTime.now().millisecondsSinceEpoch}.jpg';

      // 獲取臨時目錄
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$name');

      // 寫入檔案
      await file.writeAsBytes(imageBytes);

      // 分享檔案
      await Share.shareXFiles(
        [XFile(file.path)],
        text: text ?? '我用長輩圖產生器製作了這張圖片！',
      );

      return true;
    } catch (e) {
      print('分享圖片時發生錯誤: $e');
      return false;
    }
  }

  /// 分享到 LINE
  /// 
  /// [imageBytes] 圖片的位元組資料
  /// [fileName] 檔案名稱
  static Future<bool> shareToLine(
    Uint8List imageBytes, {
    String? fileName,
  }) async {
    try {
      // 檢查是否安裝 LINE
      if (!await _isLineInstalled()) {
        print('未安裝 LINE 應用程式');
        return false;
      }

      // 使用一般分享功能，用戶可選擇 LINE
      return await shareImage(
        imageBytes,
        fileName: fileName,
        text: '分享長輩圖到 LINE',
      );
    } catch (e) {
      print('分享到 LINE 時發生錯誤: $e');
      return false;
    }
  }

  /// 檢查儲存權限
  static Future<bool> _checkStoragePermission() async {
    if (Platform.isAndroid) {
      // Android 13+ 不需要額外權限
      if (await _isAndroid13OrHigher()) {
        return true;
      } else {
        // Android 12 以下需要儲存權限
        final status = await Permission.storage.status;
        if (status.isGranted) {
          return true;
        } else {
          final result = await Permission.storage.request();
          return result.isGranted;
        }
      }
    } else if (Platform.isIOS) {
      // iOS 需要相簿新增權限
      final status = await Permission.photosAddOnly.status;
      if (status.isGranted) {
        return true;
      } else {
        final result = await Permission.photosAddOnly.request();
        return result.isGranted;
      }
    }
    return false;
  }

  /// 檢查是否為 Android 13 或更高版本
  static Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;
    
    try {
      // 簡化的版本檢查
      return true; // 假設為新版本
    } catch (e) {
      return false;
    }
  }

  /// 檢查是否安裝 LINE
  static Future<bool> _isLineInstalled() async {
    // 簡化檢查，實際應用中可使用 url_launcher 檢查
    return true;
  }

  /// 清理臨時檔案
  /// 
  /// 清理分享時產生的臨時檔案
  static Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();
      
      for (final file in files) {
        if (file.path.contains('長輩圖_') && file.path.endsWith('.jpg')) {
          await file.delete();
        }
      }
    } catch (e) {
      print('清理臨時檔案時發生錯誤: $e');
    }
  }
}
