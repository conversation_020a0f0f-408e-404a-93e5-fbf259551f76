import 'package:flutter/material.dart';

/// 文字樣式模型
/// 
/// 定義長輩圖中文字的樣式屬性
class TextStyleModel {
  /// 文字內容
  final String text;
  
  /// 文字位置 (相對於圖片的比例位置)
  final Offset position;
  
  /// 字體大小
  final double fontSize;
  
  /// 文字顏色
  final Color textColor;
  
  /// 背景顏色 (可為 null 表示透明)
  final Color? backgroundColor;
  
  /// 背景透明度 (0.0 - 1.0)
  final double backgroundOpacity;
  
  /// 字體粗細
  final FontWeight fontWeight;
  
  /// 文字對齊方式
  final TextAlign textAlign;
  
  /// 是否顯示背景
  final bool showBackground;
  
  /// 文字陰影
  final bool hasShadow;
  
  /// 陰影顏色
  final Color shadowColor;
  
  /// 最大寬度 (相對於圖片寬度的比例)
  final double maxWidth;

  const TextStyleModel({
    required this.text,
    required this.position,
    this.fontSize = 32.0,
    this.textColor = Colors.red,
    this.backgroundColor,
    this.backgroundOpacity = 0.7,
    this.fontWeight = FontWeight.bold,
    this.textAlign = TextAlign.center,
    this.showBackground = false,
    this.hasShadow = true,
    this.shadowColor = Colors.black,
    this.maxWidth = 0.8,
  });

  /// 複製並修改部分屬性
  TextStyleModel copyWith({
    String? text,
    Offset? position,
    double? fontSize,
    Color? textColor,
    Color? backgroundColor,
    double? backgroundOpacity,
    FontWeight? fontWeight,
    TextAlign? textAlign,
    bool? showBackground,
    bool? hasShadow,
    Color? shadowColor,
    double? maxWidth,
  }) {
    return TextStyleModel(
      text: text ?? this.text,
      position: position ?? this.position,
      fontSize: fontSize ?? this.fontSize,
      textColor: textColor ?? this.textColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      backgroundOpacity: backgroundOpacity ?? this.backgroundOpacity,
      fontWeight: fontWeight ?? this.fontWeight,
      textAlign: textAlign ?? this.textAlign,
      showBackground: showBackground ?? this.showBackground,
      hasShadow: hasShadow ?? this.hasShadow,
      shadowColor: shadowColor ?? this.shadowColor,
      maxWidth: maxWidth ?? this.maxWidth,
    );
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'position': {'dx': position.dx, 'dy': position.dy},
      'fontSize': fontSize,
      'textColor': textColor.value,
      'backgroundColor': backgroundColor?.value,
      'backgroundOpacity': backgroundOpacity,
      'fontWeight': fontWeight.index,
      'textAlign': textAlign.index,
      'showBackground': showBackground,
      'hasShadow': hasShadow,
      'shadowColor': shadowColor.value,
      'maxWidth': maxWidth,
    };
  }

  /// 從 JSON 建立
  factory TextStyleModel.fromJson(Map<String, dynamic> json) {
    return TextStyleModel(
      text: json['text'] ?? '',
      position: Offset(
        json['position']['dx']?.toDouble() ?? 0.5,
        json['position']['dy']?.toDouble() ?? 0.5,
      ),
      fontSize: json['fontSize']?.toDouble() ?? 32.0,
      textColor: Color(json['textColor'] ?? Colors.red.value),
      backgroundColor: json['backgroundColor'] != null 
          ? Color(json['backgroundColor']) 
          : null,
      backgroundOpacity: json['backgroundOpacity']?.toDouble() ?? 0.7,
      fontWeight: FontWeight.values[json['fontWeight'] ?? FontWeight.bold.index],
      textAlign: TextAlign.values[json['textAlign'] ?? TextAlign.center.index],
      showBackground: json['showBackground'] ?? false,
      hasShadow: json['hasShadow'] ?? true,
      shadowColor: Color(json['shadowColor'] ?? Colors.black.value),
      maxWidth: json['maxWidth']?.toDouble() ?? 0.8,
    );
  }

  @override
  String toString() {
    return 'TextStyleModel(text: $text, position: $position, fontSize: $fontSize)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TextStyleModel &&
        other.text == text &&
        other.position == position &&
        other.fontSize == fontSize &&
        other.textColor == textColor &&
        other.backgroundColor == backgroundColor &&
        other.backgroundOpacity == backgroundOpacity &&
        other.fontWeight == fontWeight &&
        other.textAlign == textAlign &&
        other.showBackground == showBackground &&
        other.hasShadow == hasShadow &&
        other.shadowColor == shadowColor &&
        other.maxWidth == maxWidth;
  }

  @override
  int get hashCode {
    return Object.hash(
      text,
      position,
      fontSize,
      textColor,
      backgroundColor,
      backgroundOpacity,
      fontWeight,
      textAlign,
      showBackground,
      hasShadow,
      shadowColor,
      maxWidth,
    );
  }
}
