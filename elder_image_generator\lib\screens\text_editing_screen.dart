import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../models/text_style_model.dart';
import '../widgets/image_text_editor.dart';
import '../widgets/text_style_panel.dart';
import 'preview_screen.dart';

/// 文字編輯畫面
/// 
/// 提供文字編輯功能：
/// - 輸入祝福語
/// - 調整文字樣式
/// - 拖曳文字位置
class TextEditingScreen extends StatefulWidget {
  final Uint8List imageBytes;

  const TextEditingScreen({
    super.key,
    required this.imageBytes,
  });

  @override
  State<TextEditingScreen> createState() => _TextEditingScreenState();
}

class _TextEditingScreenState extends State<TextEditingScreen> {
  late TextStyleModel _textStyle;
  final TextEditingController _textController = TextEditingController();
  bool _showStylePanel = false;

  @override
  void initState() {
    super.initState();
    _initializeTextStyle();
  }

  /// 初始化文字樣式
  void _initializeTextStyle() {
    _textStyle = const TextStyleModel(
      text: '',
      position: Offset(0.5, 0.5), // 中心位置
      fontSize: 32.0,
      textColor: Colors.red,
      fontWeight: FontWeight.bold,
      textAlign: TextAlign.center,
      hasShadow: true,
      shadowColor: Colors.black,
    );
  }

  /// 更新文字內容
  void _updateText(String text) {
    setState(() {
      _textStyle = _textStyle.copyWith(text: text);
    });
  }

  /// 更新文字樣式
  void _updateTextStyle(TextStyleModel newStyle) {
    setState(() {
      _textStyle = newStyle;
    });
  }

  /// 更新文字位置
  void _updateTextPosition(Offset position) {
    setState(() {
      _textStyle = _textStyle.copyWith(position: position);
    });
  }

  /// 顯示預設祝福語選擇
  void _showBlessingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('選擇祝福語'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: AppConstants.defaultBlessings.length,
            itemBuilder: (context, index) {
              final blessing = AppConstants.defaultBlessings[index];
              return ListTile(
                title: Text(
                  blessing,
                  style: const TextStyle(fontSize: 18),
                ),
                onTap: () {
                  _textController.text = blessing;
                  _updateText(blessing);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 前往預覽畫面
  void _goToPreview() {
    if (_textStyle.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text(AppConstants.hintEnterText)),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PreviewScreen(
          imageBytes: widget.imageBytes,
          textStyle: _textStyle,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.titleEditText),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _goToPreview,
            icon: const Icon(Icons.preview),
            tooltip: '預覽',
          ),
        ],
      ),
      body: Column(
        children: [
          // 圖片編輯區域
          Expanded(
            flex: 3,
            child: ImageTextEditor(
              imageBytes: widget.imageBytes,
              textStyle: _textStyle,
              onPositionChanged: _updateTextPosition,
            ),
          ),
          
          // 文字輸入區域
          Container(
            padding: const EdgeInsets.all(AppConstants.padding),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(
                top: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Column(
              children: [
                // 文字輸入框
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _textController,
                        onChanged: _updateText,
                        decoration: const InputDecoration(
                          hintText: AppConstants.hintEnterText,
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        style: const TextStyle(fontSize: 18),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: _showBlessingsDialog,
                      icon: const Icon(Icons.list),
                      tooltip: '選擇預設祝福語',
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // 控制按鈕
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _showStylePanel = !_showStylePanel;
                          });
                        },
                        icon: const Icon(Icons.palette),
                        label: const Text('調整樣式'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _goToPreview,
                        icon: const Icon(Icons.arrow_forward),
                        label: const Text(AppConstants.buttonNext),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // 樣式調整面板
          if (_showStylePanel)
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: TextStylePanel(
                textStyle: _textStyle,
                onStyleChanged: _updateTextStyle,
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}
